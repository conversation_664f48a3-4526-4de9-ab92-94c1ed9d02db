version: '1.0'

services:
  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bedsharing_serve
    ports:
      - "18005:8005"
    environment:
      # 数据库配置
      - DATABASE_URL=mysql+pymysql://bedsharing:password@mysql:3306/my_database
      - DATABASE_ECHO=false
      
      # 应用配置
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8005
      - LOG_LEVEL=INFO
      
      # 安全配置
      - SECRET_KEY=wangzhixin666666666
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      
      # LDAP配置
      - LDAP_URI=ldap://**************:389
      - LDAP_BASE_DN_USERS=dc=users,dc=appdata,dc=erayt,dc=com
      - LDAP_CONNECT_TIMEOUT=3
      
      # 业务配置
      - UNIT_COST_PER_BED_DAY=100.0
      - EXPORT_MAX_RECORDS=10000
      - EXPORT_TIMEOUT=300
      - CACHE_TTL=3600

    volumes:
      - ./logs:/app/logs

