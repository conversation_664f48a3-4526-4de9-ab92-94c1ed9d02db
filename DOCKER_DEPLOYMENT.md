# Docker 部署指南

## 文件说明

- `Dockerfile`: 后端应用的 Docker 镜像构建文件
- `docker-compose.yml`: 开发环境的 Docker Compose 配置
- `docker-compose.prod.yml`: 生产环境的 Docker Compose 配置
- `.dockerignore`: Docker 构建时忽略的文件和目录

## 快速开始

### 开发环境部署

1. 确保已安装 Docker 和 Docker Compose

2. 启动服务：
```bash
docker-compose up -d
```

3. 查看服务状态：
```bash
docker-compose ps
```

4. 查看日志：
```bash
docker-compose logs -f backend
```

### 生产环境部署

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，设置生产环境的配置：
```bash
# 必须修改的配置
MYSQL_ROOT_PASSWORD=your_strong_root_password
MYSQL_PASSWORD=your_strong_password
SECRET_KEY=your-very-strong-secret-key-at-least-32-characters-long
LDAP_URI=ldap://your-ldap-server:389
LDAP_BASE_DN_USERS=dc=users,dc=your-domain,dc=com
```

3. 启动生产环境：
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 服务访问

- **后端 API**: http://localhost:8005
- **API 文档**: http://localhost:8005/docs
- **数据库管理**: http://localhost:8080 (Adminer)
- **MySQL 数据库**: localhost:3306

## 常用命令

### 构建和启动
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 重新构建并启动
docker-compose up -d --build
```

### 管理服务
```bash
# 停止服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]
```

### 数据库操作
```bash
# 进入 MySQL 容器
docker-compose exec mysql mysql -u root -p

# 备份数据库
docker-compose exec mysql mysqldump -u root -p my_database > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p my_database < backup.sql
```

### 应用操作
```bash
# 进入后端容器
docker-compose exec backend bash

# 查看后端日志
docker-compose logs -f backend

# 重启后端服务
docker-compose restart backend
```

## 健康检查

所有服务都配置了健康检查：

- **MySQL**: 检查数据库连接
- **后端**: 检查 HTTP 健康端点
- **依赖关系**: 后端等待 MySQL 健康后启动

## 数据持久化

- MySQL 数据存储在 Docker 卷 `mysql_data` 中
- 应用日志映射到主机的 `./logs` 目录
- 静态文件映射到主机的 `./static` 目录

## 安全注意事项

1. **生产环境必须修改**：
   - `SECRET_KEY`: 用于 JWT 签名的密钥
   - `MYSQL_ROOT_PASSWORD`: MySQL root 密码
   - `MYSQL_PASSWORD`: 应用数据库用户密码

2. **网络安全**：
   - 生产环境建议使用 HTTPS
   - 限制数据库端口的外部访问
   - 配置防火墙规则

3. **资源限制**：
   - 生产环境配置了 CPU 和内存限制
   - 根据实际需求调整资源配置

## 故障排除

### 常见问题

1. **端口冲突**：
   - 修改 `docker-compose.yml` 中的端口映射
   - 检查主机端口是否被占用

2. **数据库连接失败**：
   - 检查 MySQL 容器是否正常启动
   - 验证数据库连接字符串
   - 查看数据库日志

3. **应用启动失败**：
   - 检查环境变量配置
   - 查看应用日志
   - 验证依赖服务状态

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs mysql

# 实时跟踪日志
docker-compose logs -f --tail=100 backend
```

## 监控和维护

建议定期执行以下维护任务：

1. **备份数据库**
2. **清理 Docker 镜像和容器**
3. **更新依赖和镜像**
4. **监控资源使用情况**
5. **检查日志文件大小**
