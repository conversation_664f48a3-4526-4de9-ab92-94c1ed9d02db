"""
应用配置模块
"""
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = Field(default="宿舍入住管理系统", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")

    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8005, description="服务器端口")

    # 数据库配置
    database_url: str = Field(
        default="mysql+pymysql://root:password@localhost:3306/my_database",
        description="数据库连接URL"
    )
    database_echo: bool = Field(default=False, description="是否打印SQL语句")

    # 安全配置
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT密钥"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")

    # LDAP配置
    ldap_uri: str = Field(default="ldap://**************:389", description="LDAP服务器地址")
    ldap_base_dn_users: str = Field(
        default="dc=users,dc=appdata,dc=erayt,dc=com",
        description="LDAP用户基础DN"
    )
    ldap_connect_timeout: int = Field(default=3, description="LDAP连接超时时间(秒)")

    # CORS配置
    cors_origins: list[str] = Field(
        default=[
            "http://localhost:3000",
        ],
        description="允许的跨域源"
    )

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")

    # 业务配置
    unit_cost_per_bed_day: float = Field(default=100.0, description="每床每日费用单价")

    # 导出配置
    export_max_records: int = Field(default=10000, description="导出最大记录数")
    export_timeout: int = Field(default=300, description="导出超时时间(秒)")

    # 缓存配置
    cache_ttl: int = Field(default=3600, description="缓存生存时间(秒)")

    # API配置
    api_v1_prefix: str = Field(default="/api/v1", description="API v1前缀")

    # 端口配置（用于兼容性）
    backend_port: Optional[int] = Field(default=None, description="后端端口")
    frontend_port: Optional[int] = Field(default=None, description="前端端口")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
